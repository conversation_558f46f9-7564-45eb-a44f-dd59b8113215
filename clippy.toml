# Clippy Configuration for Shreder Project
# Based on TypeScript/JavaScript ESLint configurations and CODE_STYLES.md

# Cognitive complexity (similar to ESLint's complexity rules)
cognitive-complexity-threshold = 30

# Function length limits (consistent with 30-line function rule)
too-many-lines-threshold = 30

# Argument count limits
too-many-arguments-threshold = 7
type-complexity-threshold = 250

# Naming conventions
enum-variant-name-threshold = 3

# Performance and efficiency
trivial-copy-size-limit = 128

# Collection and iterator preferences
array-size-threshold = 512000
enum-variant-size-threshold = 200

# Error handling preferences
large-error-threshold = 128

# Arithmetic and numeric operations
unreadable-literal-lint-fractions = true

# Import and module organization
allow-expect-in-tests = true
allow-unwrap-in-tests = true

# Verbose bit mask threshold
verbose-bit-mask-threshold = 1

# Avoid breaking changes
avoid-breaking-exported-api = true
check-private-items = false