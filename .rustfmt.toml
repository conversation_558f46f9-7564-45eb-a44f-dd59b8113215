# Rustfmt Configuration for Shreder Project
# Based on TypeScript/JavaScript ESLint configurations and CODE_STYLES.md
# Using only stable features for compatibility

# Basic formatting
edition = "2024"
hard_tabs = false
tab_spaces = 4
max_width = 120
newline_style = "Unix"

# Indentation and spacing
use_small_heuristics = "Default"

# Function formatting
fn_params_layout = "Tall"
fn_call_width = 60

# Import organization
reorder_imports = true
reorder_modules = true

# Match formatting
match_block_trailing_comma = false
match_arm_leading_pipes = "Never"

# Expression formatting
short_array_element_width_threshold = 10
chain_width = 120
single_line_if_else_max_width = 0

# Miscellaneous
remove_nested_parens = true
use_field_init_shorthand = true
force_explicit_abi = true
