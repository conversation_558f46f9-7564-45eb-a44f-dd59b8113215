//! Test module to validate all code style rules and configurations
//!
//! This module contains various code patterns to test:
//! - Formatting rules from .rustfmt.toml
//! - Linting rules from clippy.toml
//! - Code style guidelines from CODE_STYLES.md

use std::collections::HashMap;
use std::fmt::Display;

// Test naming conventions
const MAX_RETRY_COUNT: u32 = 3;
const DEFAULT_TIMEOUT: u64 = 30;

// Test struct organization and formatting
#[derive(Debug, Clone, PartialEq, Eq, Hash, Copy)]
pub struct UserId(u64);

#[derive(Debug, Clone)]
pub struct UserProfile {
    // Required identification
    pub id: UserId,
    pub email: String,
    pub username: String,

    // Profile information
    pub display_name: Option<String>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,

    // Metadata
    pub is_active: bool,
    pub created_at: u64,
    pub updated_at: u64,
}

// Test enum formatting
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum MessageType {
    Info,
    Warning,
    Error { code: u32, message: String },
    Success { data: String },
}

// Test error handling patterns
#[derive(Debug)]
pub enum UserError {
    NotFound { id: u64 },
    InvalidEmail { email: String },
    ValidationFailed { field: String, reason: String },
}

impl Display for UserError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserError::NotFound { id } => write!(f, "User not found: {}", id),
            UserError::InvalidEmail { email } => write!(f, "Invalid email format: {}", email),
            UserError::ValidationFailed { field, reason } => {
                write!(f, "Validation failed for {}: {}", field, reason)
            }
        }
    }
}

// Test trait implementations
impl From<u64> for UserId {
    fn from(id: u64) -> Self {
        Self(id)
    }
}

impl Display for UserId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "UserId({})", self.0)
    }
}

impl UserProfile {
    // Test function formatting and organization
    pub fn new(id: u64, email: String, username: String) -> Self {
        let now = current_timestamp();

        Self {
            id: UserId(id),
            email,
            username,
            display_name: None,
            bio: None,
            avatar_url: None,
            is_active: true,
            created_at: now,
            updated_at: now,
        }
    }

    // Test method with multiple parameters (should not exceed 7 args limit)
    pub fn update_profile(
        &mut self,
        display_name: Option<String>,
        bio: Option<String>,
        avatar_url: Option<String>,
    ) -> Result<(), UserError> {
        self.display_name = display_name;
        self.bio = bio;
        self.avatar_url = avatar_url;
        self.updated_at = current_timestamp();

        Ok(())
    }

    // Test early return pattern
    pub fn validate_email(email: &str) -> Result<(), UserError> {
        if email.is_empty() {
            return Err(UserError::ValidationFailed {
                field: "email".to_string(),
                reason: "Email cannot be empty".to_string(),
            });
        }

        if !email.contains('@') {
            return Err(UserError::InvalidEmail {
                email: email.to_string(),
            });
        }

        if email.len() > 100 {
            return Err(UserError::ValidationFailed {
                field: "email".to_string(),
                reason: "Email too long".to_string(),
            });
        }

        Ok(())
    }

    // Test function length (should be under 30 lines)
    pub fn format_display_info(&self) -> String {
        let display_name = self.display_name.as_ref().unwrap_or(&self.username);

        let status = if self.is_active {
            "active"
        } else {
            "inactive"
        };

        format!("{} <{}> ({})", display_name, self.email, status)
    }
}

// Test collection and iterator patterns
pub fn process_active_users(users: &[UserProfile]) -> Vec<String> {
    users.iter().filter(|user| user.is_active).map(|user| user.format_display_info()).collect()
}

pub fn find_user_by_email<'a>(users: &'a [UserProfile], _email: &str) -> Option<&'a UserProfile> {
    users.iter().find(|user| user.email == _email)
}

pub fn create_user_index(users: Vec<UserProfile>) -> HashMap<UserId, UserProfile> {
    users.into_iter().map(|user| (user.id, user)).collect()
}

// Test string handling patterns
pub fn normalize_email(email: &str) -> String {
    email.trim().to_lowercase()
}

pub fn format_user_list(users: &[UserProfile]) -> String {
    let mut result = String::with_capacity(256);

    for (i, user) in users.iter().enumerate() {
        if i > 0 {
            result.push_str(", ");
        }
        result.push_str(&user.format_display_info());
    }

    result
}

// Test pattern matching
pub fn handle_message(msg_type: MessageType) -> String {
    match msg_type {
        MessageType::Info => "Information message".to_string(),
        MessageType::Warning => "Warning message".to_string(),
        MessageType::Error { code, message } => {
            format!("Error {}: {}", code, message)
        }
        MessageType::Success { data } => {
            format!("Success: {}", data)
        }
    }
}

// Test simple pattern with if let
pub fn extract_error_code(msg_type: &MessageType) -> Option<u32> {
    if let MessageType::Error { code, .. } = msg_type {
        Some(*code)
    } else {
        None
    }
}

// Test variable declaration patterns
pub fn process_user_data(user: &UserProfile) -> Result<String, UserError> {
    // Configuration values
    const MIN_USERNAME_LENGTH: usize = 3;
    const MAX_BIO_LENGTH: usize = 500;

    // Validation checks
    let username_valid = user.username.len() >= MIN_USERNAME_LENGTH;
    let bio_valid = user.bio.as_ref().map_or(true, |bio| bio.len() <= MAX_BIO_LENGTH);

    // Early returns for validation failures
    if !username_valid {
        return Err(UserError::ValidationFailed {
            field: "username".to_string(),
            reason: "Username too short".to_string(),
        });
    }

    if !bio_valid {
        return Err(UserError::ValidationFailed {
            field: "bio".to_string(),
            reason: "Bio too long".to_string(),
        });
    }

    // Processing logic
    let processed_data = format!(
        "User: {} ({}), Active: {}",
        user.username, user.email, user.is_active
    );

    Ok(processed_data)
}

// Test cognitive complexity (should be under threshold)
pub fn calculate_user_score(user: &UserProfile) -> u32 {
    let mut score = 0;

    // Base score
    score += 10;

    // Email bonus
    if user.email.contains('@') {
        score += 5;
    }

    // Profile completeness bonus
    if user.display_name.is_some() {
        score += 3;
    }

    if user.bio.is_some() {
        score += 2;
    }

    if user.avatar_url.is_some() {
        score += 1;
    }

    // Activity bonus
    if user.is_active {
        score += 10;
    }

    score
}

// Helper function
fn current_timestamp() -> u64 {
    std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap_or_default().as_secs()
}

// Test method chaining formatting
fn process_email_list(emails: &[String]) -> Vec<String> {
    emails.iter().map(|email| email.trim().to_lowercase()).filter(|email| email.contains('@')).collect()
}

// Test long method chain with text that exceeds 120 chars
#[rustfmt::skip]
fn process_long_error_message(input: &str) -> String {
    input.trim().to_lowercase().replace("this is a very long error message that definitely exceeds the 120 character limit", "short error").chars().collect::<String>().trim().to_string()
}

// Alternative approach: Use #[rustfmt::skip] attribute
#[rustfmt::skip]
fn process_another_long_chain(input: &str) -> String {
    input.trim().to_lowercase().replace("another very long text that would normally be broken into multiple lines by rustfmt", "short").chars().collect::<String>()
}

// Test if-else formatting (should always use braces and newlines)
fn get_user_status(user: &UserProfile) -> String {
    if user.is_active {
        "User is currently active and available".to_string()
    } else {
        "User is inactive".to_string()
    }
}

fn calculate_discount(amount: f64, is_premium: bool) -> f64 {
    if is_premium {
        amount * 0.8
    } else {
        amount * 0.9
    }
}

// Test module for unit tests
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_creation() {
        let user = UserProfile::new(1, "<EMAIL>".to_string(), "testuser".to_string());

        assert_eq!(user.id.0, 1);
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.username, "testuser");
        assert!(user.is_active);
    }

    #[test]
    fn test_email_validation() {
        assert!(UserProfile::validate_email("<EMAIL>").is_ok());
        assert!(UserProfile::validate_email("").is_err());
        assert!(UserProfile::validate_email("invalid-email").is_err());
    }

    #[test]
    fn test_user_score_calculation() {
        let user = UserProfile::new(1, "<EMAIL>".to_string(), "testuser".to_string());

        let score = calculate_user_score(&user);
        assert!(score > 0);
    }

    #[test]
    fn test_message_handling() {
        let info_msg = MessageType::Info;
        let result = handle_message(info_msg);
        assert_eq!(result, "Information message");

        let error_msg = MessageType::Error {
            code: 404,
            message: "Not found".to_string(),
        };
        let result = handle_message(error_msg);
        assert!(result.contains("404"));
    }

    #[test]
    fn test_user_filtering() {
        let users = vec![
            UserProfile::new(1, "<EMAIL>".to_string(), "user1".to_string()),
            UserProfile::new(2, "<EMAIL>".to_string(), "user2".to_string()),
        ];

        let active_users = process_active_users(&users);
        assert_eq!(active_users.len(), 2);
    }
}
