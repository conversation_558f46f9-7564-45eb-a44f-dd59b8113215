# Rust Code Style Guidelines

This document provides comprehensive coding guidelines for AI assistants working on the <PERSON>hr<PERSON><PERSON> project. These guidelines are derived from TypeScript/JavaScript best practices and adapted for Rust development.

## Project Overview

The **Shreder** project is a Rust application focused on high-performance data processing. This document ensures consistent, maintainable, and idiomatic Rust code across the entire codebase.

## Rust Configuration

### Project Settings

-   **Edition**: 2024 (latest stable Rust)
-   **Formatting**: Use `rustfmt` with default configuration
-   **Linting**: Use `clippy` with strict warnings enabled
-   **Safety**: Prefer safe Rust, avoid `unsafe` unless absolutely necessary

### Code Quality Standards

-   Use `Result<T, E>` for all fallible operations
-   Handle errors explicitly with meaningful error messages
-   Use `Option<T>` for nullable values
-   Leverage Rust's type system for compile-time safety
-   Follow ownership and borrowing principles strictly

## Formatting Rules

### General Formatting

-   Use **4 spaces** for indentation (consistent with TypeScript projects)
-   Maximum line length of **120 characters**
-   Use Unix line endings (LF)
-   Remove trailing whitespace
-   Files must end with a newline
-   Use `rustfmt` for automatic formatting

### Code Structure

-   Use **1TBS brace style** (one true brace style)
-   Always use braces for control structures, even single statements
-   Place opening brace on the same line as the declaration

```rust
// Correct
if condition {
    do_something();
}

// Incorrect
if condition
{
    do_something();
}
```

### Spacing Rules

-   Space after commas: `vec![item1, item2, item3]`
-   Space around operators: `a + b`, `x == y`
-   Space after keywords: `if condition`, `for item in items`
-   No space before function parentheses: `fn function_name()`
-   Space before opening braces: `struct Name {`

## Naming Conventions

### Variables and Functions

-   Use **snake_case** for variables, functions, and modules
-   Use descriptive names that clearly indicate purpose
-   Prefix unused parameters with underscore: `_unused_param`

```rust
// Correct
let user_count = 10;
fn calculate_total_price() -> f64 { }
fn process_data(_unused_input: &str) { }

// Incorrect
let userCount = 10;
fn calculateTotalPrice() -> f64 { }
```

### Types and Constants

-   Use **PascalCase** for types, structs, enums, and traits
-   Use **SCREAMING_SNAKE_CASE** for constants and static variables

```rust
// Correct
struct UserAccount { }
enum MessageType { }
trait DataProcessor { }
const MAX_RETRY_COUNT: u32 = 3;

// Incorrect
struct user_account { }
enum messageType { }
const max_retry_count: u32 = 3;
```

### Files and Modules

-   Use **snake_case** for file names: `user_service.rs`, `database_config.rs`
-   Module names should match file names
-   Use descriptive names that indicate module purpose

## Code Organization

### Module Structure

```
src/
├── main.rs              # Application entry point
├── lib.rs               # Library root (if applicable)
├── config/              # Configuration management
├── services/            # Business logic services
├── models/              # Data structures and types
├── utils/               # Utility functions and helpers
├── errors/              # Custom error types
└── tests/               # Integration tests
```

### Import Organization

Group imports in the following order with blank lines between groups:

1. **Standard library** (`std::`, `core::`)
2. **External crates** (alphabetical order)
3. **Internal modules** (relative imports)

```rust
// Standard library
use std::collections::HashMap;
use std::fs::File;

// External crates
use serde::{Deserialize, Serialize};
use tokio::time::Duration;

// Internal modules
use crate::config::AppConfig;
use crate::services::UserService;
```

### Function Organization

-   Keep functions focused on single responsibility
-   **Maximum 30 lines per function**
-   **Maximum 3 levels of nesting depth**
-   Extract complex logic into separate functions

```rust
// Good: Single responsibility, clear purpose
fn calculate_user_score(user: &User) -> Result<u32, ScoreError> {
    validate_user(user)?;
    let base_score = compute_base_score(user);
    let bonus = calculate_bonus(user);
    Ok(base_score + bonus)
}

// Bad: Too many responsibilities
fn process_user_data(user: &User) -> Result<ProcessedUser, ProcessError> {
    // 50+ lines of mixed validation, calculation, and formatting
}
```

## Error Handling

### Error Types

-   Create custom error types for different error categories
-   Use `thiserror` crate for error derivation
-   Implement proper error propagation with `?` operator

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum UserError {
    #[error("User not found: {id}")]
    NotFound { id: u64 },

    #[error("Invalid email format: {email}")]
    InvalidEmail { email: String },

    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
}
```

### Error Handling Patterns

-   Use `Result<T, E>` for all fallible operations
-   Provide meaningful error messages with context
-   Fail fast: detect and report errors early
-   Use `?` operator for error propagation

```rust
// Good: Clear error handling with context
fn load_user_config(path: &Path) -> Result<UserConfig, ConfigError> {
    let content = fs::read_to_string(path)
        .map_err(|e| ConfigError::FileRead { path: path.to_owned(), source: e })?;

    let config: UserConfig = toml::from_str(&content)
        .map_err(|e| ConfigError::ParseError { source: e })?;

    Ok(config)
}
```

## Memory Management

### Ownership Patterns

-   Understand and follow Rust's ownership rules
-   Use references (`&T`) when you don't need ownership
-   Use `Arc<T>` for shared ownership across threads
-   Use `Rc<T>` for shared ownership in single-threaded contexts

```rust
// Good: Clear ownership semantics
fn process_data(data: &[u8]) -> Result<ProcessedData, ProcessError> {
    // Process without taking ownership
}

fn store_data(data: ProcessedData) -> Result<(), StoreError> {
    // Take ownership when data needs to be moved
}
```

### Lifetime Management

-   Use explicit lifetimes when necessary
-   Keep lifetime annotations minimal and clear
-   Prefer owned types over complex lifetime relationships

```rust
// Good: Clear lifetime relationship
struct DataProcessor<'a> {
    config: &'a Config,
    buffer: Vec<u8>,
}

// Better: Avoid lifetimes when possible
struct DataProcessor {
    config: Arc<Config>,
    buffer: Vec<u8>,
}
```

## Performance Guidelines

### Optimization Principles

-   Profile before optimizing
-   Use appropriate data structures for specific use cases
-   Avoid unnecessary allocations in hot paths
-   Leverage iterators efficiently
-   Consider zero-copy operations when possible

```rust
// Good: Efficient iterator usage
fn sum_even_numbers(numbers: &[i32]) -> i32 {
    numbers
        .iter()
        .filter(|&&n| n % 2 == 0)
        .sum()
}

// Bad: Unnecessary allocations
fn sum_even_numbers(numbers: &[i32]) -> i32 {
    let even_numbers: Vec<i32> = numbers
        .iter()
        .filter(|&&n| n % 2 == 0)
        .cloned()
        .collect();
    even_numbers.iter().sum()
}
```

## Testing Guidelines

### Test Organization

```
tests/
├── common/              # Shared testing utilities
│   ├── fixtures.rs      # Test data fixtures
│   └── helpers.rs       # Test helper functions
├── integration/         # Integration tests
└── unit/               # Unit tests (if separate from src/)
```

### Testing Principles

-   Write tests for all public APIs
-   Use descriptive test names that explain the scenario
-   Test both success and failure cases
-   Use `#[cfg(test)]` for test-only code in source files

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn calculate_score_returns_correct_value_for_valid_user() {
        let user = User::new("<EMAIL>", 25);
        let result = calculate_user_score(&user);
        assert_eq!(result.unwrap(), 100);
    }

    #[test]
    fn calculate_score_returns_error_for_invalid_user() {
        let user = User::new("", 0);
        let result = calculate_user_score(&user);
        assert!(result.is_err());
    }
}
```

## Documentation Guidelines

### Code Documentation

-   Use `///` for public API documentation
-   Include examples in documentation when helpful
-   Document complex algorithms and business logic
-   Keep documentation concise and focused

````rust
/// Calculates the user score based on activity and engagement metrics.
///
/// # Arguments
///
/// * `user` - The user to calculate the score for
///
/// # Returns
///
/// Returns the calculated score or an error if the user data is invalid.
///
/// # Examples
///
/// ```
/// let user = User::new("<EMAIL>", 25);
/// let score = calculate_user_score(&user)?;
/// assert!(score > 0);
/// ```
pub fn calculate_user_score(user: &User) -> Result<u32, ScoreError> {
    // Implementation
}
````

## Dependency Management

### Cargo.toml Organization

-   Group dependencies logically
-   Use specific version constraints
-   Minimize dependencies to reduce attack surface
-   Use workspace dependencies for multi-crate projects

```toml
[dependencies]
# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Error handling
thiserror = "1.0"
anyhow = "1.0"

[dev-dependencies]
# Testing
tokio-test = "0.4"
```

## Tools and Commands

### Essential Commands

-   `cargo fmt` - Format code according to style guidelines
-   `cargo clippy` - Run linter with strict warnings
-   `cargo test` - Run all tests
-   `cargo check` - Fast compilation check
-   `cargo build --release` - Optimized build

### Recommended Tools

-   **rust-analyzer** - IDE integration for VS Code
-   **cargo-watch** - Automatic rebuilding on file changes
-   **cargo-audit** - Security vulnerability scanning
-   **cargo-outdated** - Check for outdated dependencies

## Implementation Rules

### Scope Limitation

-   Implement only what is explicitly requested
-   Avoid adding features beyond requirements
-   Ask before implementing related improvements
-   Focus on minimal viable implementation

### Default Exclusions

Unless explicitly requested, do not add:

-   Extensive input validation beyond basic needs
-   Comprehensive error handling for edge cases
-   Example implementations or demo code
-   Complex optimization without profiling data

### Code Quality

-   Never generate comments in source code by default
-   Use self-documenting code through clear naming
-   Keep functions focused on single responsibility
-   Maintain consistent code organization across modules
-   Eliminate all unused code: imports, variables, functions

## Advanced Code Style Rules

### Blank Line Management

-   **Always add blank lines** before and after:
    -   Function definitions
    -   Struct and enum definitions
    -   Impl blocks
    -   Module declarations
    -   Major code blocks (loops, conditionals with multiple statements)
    -   Return statements (when not the only statement in a block)

```rust
// Good: Proper blank line usage
fn process_user(user: &User) -> Result<ProcessedUser, UserError> {
    validate_user(user)?;

    let processed_data = transform_user_data(user);

    save_to_database(&processed_data)?;

    Ok(processed_data)
}

struct UserAccount {
    id: u64,
    email: String,
}

impl UserAccount {
    fn new(id: u64, email: String) -> Self {
        Self { id, email }
    }
}
```

### Control Flow Formatting

-   **Always use braces** for all control structures, even single statements
-   **No else after return** - prefer early returns
-   **Keep ternary-like expressions simple** using `if` expressions

```rust
// Good: Early return pattern
fn validate_email(email: &str) -> Result<(), ValidationError> {
    if email.is_empty() {
        return Err(ValidationError::EmptyEmail);
    }

    if !email.contains('@') {
        return Err(ValidationError::InvalidFormat);
    }

    Ok(())
}

// Good: Simple conditional expression
let status = if user.is_active() { "active" } else { "inactive" };

// Bad: Complex nested conditionals
let result = if condition1 {
    if condition2 { value1 } else { value2 }
} else {
    if condition3 { value3 } else { value4 }
};
```

### Variable Declaration Patterns

-   **Use `const` by default** for immutable values
-   **Use `let` for mutable variables** only when necessary
-   **Group variable declarations** by type with blank lines between groups
-   **One variable per declaration** for clarity

```rust
// Good: Clear variable grouping
fn process_request(request: &Request) -> Result<Response, ProcessError> {
    // Configuration values
    const MAX_RETRIES: u32 = 3;
    const TIMEOUT_SECONDS: u64 = 30;

    // Mutable state
    let mut retry_count = 0;
    let mut last_error = None;

    // Processing logic
    let validated_request = validate_request(request)?;
    let processed_data = transform_data(&validated_request)?;

    Ok(Response::new(processed_data))
}
```

### Function and Method Patterns

-   **Function signatures on single line** when possible
-   **Omit explicit return types** for simple cases
-   **Maximum 30 lines per function**
-   **Maximum 3 levels of nesting depth**
-   **Prefer methods that take `&self` over associated functions** when working with instance data

```rust
// Good: Clear function signatures
impl UserService {
    pub fn find_user_by_email(&self, email: &str) -> Option<User> {
        self.users.iter().find(|u| u.email == email).cloned()
    }

    pub fn create_user(&mut self, email: String, name: String) -> Result<User, UserError> {
        self.validate_email(&email)?;

        let user = User::new(email, name);
        self.users.push(user.clone());

        Ok(user)
    }
}
```

### Struct and Enum Organization

-   **Group fields logically** in structs
-   **Use consistent field ordering**: required fields first, optional fields last
-   **Prefer explicit field names** in struct initialization
-   **Use trailing commas** in multiline struct definitions

```rust
// Good: Well-organized struct
#[derive(Debug, Clone)]
pub struct UserProfile {
    // Required identification
    pub id: u64,
    pub email: String,
    pub username: String,

    // Profile information
    pub display_name: Option<String>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,

    // Metadata
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

impl UserProfile {
    pub fn new(id: u64, email: String, username: String) -> Self {
        let now = Utc::now();

        Self {
            id,
            email,
            username,
            display_name: None,
            bio: None,
            avatar_url: None,
            created_at: now,
            updated_at: now,
            is_active: true,
        }
    }
}
```

### Pattern Matching Guidelines

-   **Use exhaustive pattern matching** when possible
-   **Prefer `if let` for simple single-pattern matches**
-   **Use `match` for complex pattern matching**
-   **Handle all cases explicitly** rather than using catch-all patterns

```rust
// Good: Exhaustive pattern matching
fn handle_response(response: ApiResponse) -> Result<Data, ApiError> {
    match response {
        ApiResponse::Success(data) => Ok(data),
        ApiResponse::NotFound => Err(ApiError::ResourceNotFound),
        ApiResponse::Unauthorized => Err(ApiError::AuthenticationFailed),
        ApiResponse::ServerError(msg) => Err(ApiError::ServerError(msg)),
    }
}

// Good: Simple pattern with if let
fn extract_user_id(token: &str) -> Option<u64> {
    if let Some(claims) = decode_token(token) {
        claims.user_id
    } else {
        None
    }
}
```

### Async/Await Patterns

-   **Use `async`/`await` consistently** for asynchronous operations
-   **Avoid blocking operations** in async contexts
-   **Handle cancellation** appropriately
-   **Use `tokio::select!` for concurrent operations**

```rust
// Good: Proper async patterns
pub async fn fetch_user_data(user_id: u64) -> Result<UserData, FetchError> {
    let user_future = fetch_user_profile(user_id);
    let settings_future = fetch_user_settings(user_id);

    let (profile, settings) = tokio::try_join!(user_future, settings_future)?;

    Ok(UserData { profile, settings })
}

pub async fn process_with_timeout(data: &[u8]) -> Result<ProcessedData, ProcessError> {
    let process_future = process_data(data);
    let timeout_future = tokio::time::sleep(Duration::from_secs(30));

    tokio::select! {
        result = process_future => result,
        _ = timeout_future => Err(ProcessError::Timeout),
    }
}
```

### Macro Usage Guidelines

-   **Prefer functions over macros** when possible
-   **Use standard library macros** (`vec!`, `format!`, `println!`) appropriately
-   **Create custom macros** only for code generation that can't be achieved with functions
-   **Document macro behavior** clearly

```rust
// Good: Appropriate macro usage
fn log_user_action(user_id: u64, action: &str) {
    println!("User {}: {}", user_id, action);
}

fn create_default_config() -> Config {
    Config {
        database_url: "sqlite://memory".to_string(),
        port: 8080,
        log_level: "info".to_string(),
    }
}

// Good: Custom macro for repetitive code generation
macro_rules! impl_from_error {
    ($error_type:ty, $variant:ident) => {
        impl From<$error_type> for AppError {
            fn from(err: $error_type) -> Self {
                AppError::$variant(err)
            }
        }
    };
}
```

## Rust-Specific Best Practices

### Trait Implementation Guidelines

-   **Implement common traits** (`Debug`, `Clone`, `PartialEq`) when appropriate
-   **Use `#[derive]` for automatic implementations** when possible
-   **Implement traits manually** only when custom behavior is needed
-   **Follow trait naming conventions** (adjectives for capabilities, nouns for types)

```rust
// Good: Appropriate trait derivations
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct UserId(u64);

#[derive(Debug, Clone)]
pub struct User {
    id: UserId,
    email: String,
    created_at: DateTime<Utc>,
}

// Good: Custom trait implementation when needed
impl Display for User {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "User({}): {}", self.id.0, self.email)
    }
}

impl From<u64> for UserId {
    fn from(id: u64) -> Self {
        Self(id)
    }
}
```

### Iterator and Collection Patterns

-   **Prefer iterators over index-based loops**
-   **Use iterator combinators** (`map`, `filter`, `fold`) for data transformation
-   **Collect only when necessary** - prefer iterator chains
-   **Use appropriate collection types** for specific use cases

```rust
// Good: Efficient iterator usage
fn process_active_users(users: &[User]) -> Vec<ProcessedUser> {
    users
        .iter()
        .filter(|user| user.is_active)
        .map(|user| ProcessedUser::from(user))
        .collect()
}

fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
    users.iter().find(|user| user.email == email)
}

// Good: Appropriate collection types
use std::collections::{HashMap, HashSet, BTreeMap, VecDeque};

fn create_user_index(users: Vec<User>) -> HashMap<UserId, User> {
    users.into_iter().map(|user| (user.id, user)).collect()
}

fn get_unique_domains(emails: &[String]) -> HashSet<String> {
    emails
        .iter()
        .filter_map(|email| email.split('@').nth(1))
        .map(|domain| domain.to_lowercase())
        .collect()
}
```

### String Handling Best Practices

-   **Use `&str` for string slices** and borrowed strings
-   **Use `String` for owned strings** that need to be modified
-   **Prefer `format!` over string concatenation** for complex formatting
-   **Use `Cow<str>` for conditional ownership**

```rust
// Good: Appropriate string types
fn validate_email(email: &str) -> Result<(), ValidationError> {
    if email.is_empty() || !email.contains('@') {
        return Err(ValidationError::InvalidFormat);
    }
    Ok(())
}

fn format_user_display(user: &User) -> String {
    format!("{} <{}>", user.name, user.email)
}

fn normalize_email(email: &str) -> String {
    email.trim().to_lowercase()
}

// Good: Using Cow for conditional ownership
use std::borrow::Cow;

fn process_text(input: &str, should_uppercase: bool) -> Cow<str> {
    if should_uppercase {
        Cow::Owned(input.to_uppercase())
    } else {
        Cow::Borrowed(input)
    }
}
```

### Configuration and Environment Management

-   **Use `serde` for configuration serialization**
-   **Support multiple configuration formats** (TOML, JSON, YAML)
-   **Use environment variables** for runtime configuration
-   **Provide sensible defaults** for all configuration options

```rust
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    #[serde(default = "default_port")]
    pub port: u16,

    #[serde(default = "default_host")]
    pub host: String,

    pub database_url: String,

    #[serde(default)]
    pub debug: bool,
}

fn default_port() -> u16 {
    8080
}

fn default_host() -> String {
    "127.0.0.1".to_string()
}

impl AppConfig {
    pub fn from_env() -> Result<Self, ConfigError> {
        let mut config = Self::default();

        if let Ok(port) = env::var("PORT") {
            config.port = port.parse().map_err(ConfigError::InvalidPort)?;
        }

        if let Ok(host) = env::var("HOST") {
            config.host = host;
        }

        config.database_url = env::var("DATABASE_URL")
            .map_err(|_| ConfigError::MissingDatabaseUrl)?;

        Ok(config)
    }
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            port: default_port(),
            host: default_host(),
            database_url: String::new(),
            debug: false,
        }
    }
}
```

### Logging and Observability

-   **Use structured logging** with appropriate log levels
-   **Include contextual information** in log messages
-   **Use `tracing` for async-aware logging**
-   **Implement proper error context** with error chains

```rust
use tracing::{debug, error, info, instrument, warn};

#[instrument(skip(self))]
pub async fn process_user_request(&self, user_id: u64, request: UserRequest) -> Result<Response, ProcessError> {
    info!(user_id, request_type = ?request.request_type, "Processing user request");

    let user = self.find_user(user_id).await
        .map_err(|e| {
            error!(user_id, error = %e, "Failed to find user");
            ProcessError::UserNotFound(user_id)
        })?;

    debug!(user_id, user_email = %user.email, "User found, processing request");

    let result = self.handle_request(&user, request).await?;

    info!(user_id, "Request processed successfully");
    Ok(result)
}

// Good: Error context with anyhow
use anyhow::{Context, Result};

fn load_config_file(path: &Path) -> Result<Config> {
    let content = std::fs::read_to_string(path)
        .with_context(|| format!("Failed to read config file: {}", path.display()))?;

    let config: Config = toml::from_str(&content)
        .with_context(|| format!("Failed to parse config file: {}", path.display()))?;

    Ok(config)
}
```

### CLI Application Patterns

-   **Use `clap` for command-line argument parsing**
-   **Implement proper exit codes** for different error conditions
-   **Provide helpful error messages** and usage information
-   **Support common CLI patterns** (verbose mode, config files, etc.)

```rust
use clap::{Parser, Subcommand};

#[derive(Parser)]
#[command(name = "shreder")]
#[command(about = "A high-performance data processing tool")]
pub struct Cli {
    #[arg(short, long, action = clap::ArgAction::Count)]
    pub verbose: u8,

    #[arg(short, long, value_name = "FILE")]
    pub config: Option<PathBuf>,

    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    Process {
        #[arg(value_name = "INPUT")]
        input: PathBuf,

        #[arg(short, long, value_name = "OUTPUT")]
        output: Option<PathBuf>,

        #[arg(long)]
        dry_run: bool,
    },
    Validate {
        #[arg(value_name = "FILE")]
        file: PathBuf,
    },
}

fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging based on verbosity
    let log_level = match cli.verbose {
        0 => "warn",
        1 => "info",
        2 => "debug",
        _ => "trace",
    };

    tracing_subscriber::fmt()
        .with_env_filter(log_level)
        .init();

    // Load configuration
    let config = if let Some(config_path) = cli.config {
        AppConfig::from_file(&config_path)?
    } else {
        AppConfig::from_env()?
    };

    // Execute command
    match cli.command {
        Commands::Process { input, output, dry_run } => {
            process_command(config, input, output, dry_run)
        }
        Commands::Validate { file } => {
            validate_command(config, file)
        }
    }
}
```

### Performance and Optimization Guidelines

-   **Profile before optimizing** using tools like `cargo flamegraph`
-   **Use `#[inline]` judiciously** for hot path functions
-   **Prefer stack allocation** over heap allocation when possible
-   **Use `Box<T>` for large structs** to avoid stack overflow
-   **Consider `Arc<T>` vs `Rc<T>`** based on threading requirements

```rust
// Good: Performance-conscious code
#[inline]
fn fast_hash(data: &[u8]) -> u64 {
    // Hot path function that benefits from inlining
    data.iter().fold(0u64, |acc, &byte| {
        acc.wrapping_mul(31).wrapping_add(byte as u64)
    })
}

// Good: Appropriate use of Box for large structs
pub struct LargeDataStructure {
    data: Box<[u8; 1024 * 1024]>, // 1MB array on heap
    metadata: SmallMetadata,       // Small struct on stack
}

// Good: Zero-copy string processing
fn extract_domain(email: &str) -> Option<&str> {
    email.split('@').nth(1)
}

// Good: Efficient string building
fn build_query(conditions: &[Condition]) -> String {
    let mut query = String::with_capacity(256); // Pre-allocate capacity
    query.push_str("SELECT * FROM users WHERE ");

    for (i, condition) in conditions.iter().enumerate() {
        if i > 0 {
            query.push_str(" AND ");
        }
        query.push_str(&condition.to_sql());
    }

    query
}
```

This document ensures consistent, high-quality Rust code that follows established patterns from TypeScript/JavaScript projects while embracing Rust's unique features and idioms.
