# Rust Code Style Guidelines

This document provides comprehensive code style guidelines for AI assistants working on the <PERSON>hr<PERSON><PERSON> project. These guidelines are derived from TypeScript/JavaScript best practices and adapted for Rust development.

## Project Overview

The **Shreder** project is a Rust application focused on high-performance data processing. This document ensures consistent, maintainable, and idiomatic Rust code style across the entire codebase.

## Configuration Files

This project uses the following configuration files for automated code style enforcement:

- **`.rustfmt.toml`** - Rust code formatting configuration
- **`clippy.toml`** - Clippy linting configuration  
- **`Cargo.toml`** - Project dependencies and metadata

## Formatting Rules

### General Formatting

- Use **4 spaces** for indentation (consistent with TypeScript projects)
- Maximum line length of **120 characters**
- Use Unix line endings (LF)
- Remove trailing whitespace
- Files must end with a newline
- Use `rustfmt` for automatic formatting

### Code Structure

- Use **1TBS brace style** (one true brace style)
- Always use braces for control structures, even single statements
- Place opening brace on the same line as the declaration

```rust
// Correct
if condition {
    do_something();
}

// Incorrect
if condition
{
    do_something();
}
```

### Spacing Rules

- Space after commas: `vec![item1, item2, item3]`
- Space around operators: `a + b`, `x == y`
- Space after keywords: `if condition`, `for item in items`
- No space before function parentheses: `fn function_name()`
- Space before opening braces: `struct Name {`

## Naming Conventions

### Variables and Functions

- Use **snake_case** for variables, functions, and modules
- Use descriptive names that clearly indicate purpose
- Prefix unused parameters with underscore: `_unused_param`

```rust
// Correct
let user_count = 10;
fn calculate_total_price() -> f64 { }
fn process_data(_unused_input: &str) { }

// Incorrect
let userCount = 10;
fn calculateTotalPrice() -> f64 { }
```

### Types and Constants

- Use **PascalCase** for types, structs, enums, and traits
- Use **SCREAMING_SNAKE_CASE** for constants and static variables

```rust
// Correct
struct UserAccount { }
enum MessageType { }
trait DataProcessor { }
const MAX_RETRY_COUNT: u32 = 3;

// Incorrect
struct user_account { }
enum messageType { }
const max_retry_count: u32 = 3;
```

### Files and Modules

- Use **snake_case** for file names: `user_service.rs`, `database_config.rs`
- Module names should match file names
- Use descriptive names that indicate module purpose

## Import Organization

Group imports in the following order with blank lines between groups:

1. **Standard library** (`std::`, `core::`)
2. **External crates** (alphabetical order)
3. **Internal modules** (relative imports)

```rust
// Standard library
use std::collections::HashMap;
use std::fs::File;

// External crates
use serde::{Deserialize, Serialize};
use tokio::time::Duration;

// Internal modules
use crate::config::AppConfig;
use crate::services::UserService;
```

## Function and Method Formatting

### Function Signatures

- Keep function signatures on single line when possible
- Maximum 30 lines per function
- Maximum 3 levels of nesting depth
- Omit explicit return types for simple cases

```rust
// Good: Clear function signatures
impl UserService {
    pub fn find_user_by_email(&self, email: &str) -> Option<User> {
        self.users.iter().find(|u| u.email == email).cloned()
    }

    pub fn create_user(&mut self, email: String, name: String) -> Result<User, UserError> {
        self.validate_email(&email)?;
        
        let user = User::new(email, name);
        self.users.push(user.clone());
        
        Ok(user)
    }
}
```

## Blank Line Management

Always add blank lines before and after:

- Function definitions
- Struct and enum definitions
- Impl blocks
- Module declarations
- Major code blocks (loops, conditionals with multiple statements)
- Return statements (when not the only statement in a block)

```rust
// Good: Proper blank line usage
fn process_user(user: &User) -> Result<ProcessedUser, UserError> {
    validate_user(user)?;

    let processed_data = transform_user_data(user);

    save_to_database(&processed_data)?;

    Ok(processed_data)
}

struct UserAccount {
    id: u64,
    email: String,
}

impl UserAccount {
    fn new(id: u64, email: String) -> Self {
        Self { id, email }
    }
}
```

## Control Flow Formatting

- Always use braces for all control structures, even single statements
- No else after return - prefer early returns
- Keep conditional expressions simple

```rust
// Good: Early return pattern
fn validate_email(email: &str) -> Result<(), ValidationError> {
    if email.is_empty() {
        return Err(ValidationError::EmptyEmail);
    }

    if !email.contains('@') {
        return Err(ValidationError::InvalidFormat);
    }

    Ok(())
}

// Good: Simple conditional expression
let status = if user.is_active() { "active" } else { "inactive" };
```

## Variable Declaration Patterns

- Use `const` by default for immutable values
- Use `let` for mutable variables only when necessary
- Group variable declarations by type with blank lines between groups
- One variable per declaration for clarity

```rust
// Good: Clear variable grouping
fn process_request(request: &Request) -> Result<Response, ProcessError> {
    // Configuration values
    const MAX_RETRIES: u32 = 3;
    const TIMEOUT_SECONDS: u64 = 30;

    // Mutable state
    let mut retry_count = 0;
    let mut last_error = None;

    // Processing logic
    let validated_request = validate_request(request)?;
    let processed_data = transform_data(&validated_request)?;

    Ok(Response::new(processed_data))
}
```

## Struct and Enum Organization

- Group fields logically in structs
- Use consistent field ordering: required fields first, optional fields last
- Prefer explicit field names in struct initialization
- Use trailing commas in multiline struct definitions

```rust
// Good: Well-organized struct
#[derive(Debug, Clone)]
pub struct UserProfile {
    // Required identification
    pub id: u64,
    pub email: String,
    pub username: String,

    // Profile information
    pub display_name: Option<String>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,

    // Metadata
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

impl UserProfile {
    pub fn new(id: u64, email: String, username: String) -> Self {
        let now = Utc::now();
        
        Self {
            id,
            email,
            username,
            display_name: None,
            bio: None,
            avatar_url: None,
            created_at: now,
            updated_at: now,
            is_active: true,
        }
    }
}
```

## Pattern Matching Style

- Use exhaustive pattern matching when possible
- Prefer `if let` for simple single-pattern matches
- Use `match` for complex pattern matching
- Handle all cases explicitly rather than using catch-all patterns

```rust
// Good: Exhaustive pattern matching
fn handle_response(response: ApiResponse) -> Result<Data, ApiError> {
    match response {
        ApiResponse::Success(data) => Ok(data),
        ApiResponse::NotFound => Err(ApiError::ResourceNotFound),
        ApiResponse::Unauthorized => Err(ApiError::AuthenticationFailed),
        ApiResponse::ServerError(msg) => Err(ApiError::ServerError(msg)),
    }
}

// Good: Simple pattern with if let
fn extract_user_id(token: &str) -> Option<u64> {
    if let Some(claims) = decode_token(token) {
        claims.user_id
    } else {
        None
    }
}
```

## String and Collection Formatting

- Use `&str` for string slices and borrowed strings
- Use `String` for owned strings that need to be modified
- Prefer `format!` over string concatenation for complex formatting
- Use appropriate collection types for specific use cases

```rust
// Good: Appropriate string types
fn validate_email(email: &str) -> Result<(), ValidationError> {
    if email.is_empty() || !email.contains('@') {
        return Err(ValidationError::InvalidFormat);
    }
    Ok(())
}

fn format_user_display(user: &User) -> String {
    format!("{} <{}>", user.name, user.email)
}

// Good: Efficient iterator usage
fn process_active_users(users: &[User]) -> Vec<ProcessedUser> {
    users
        .iter()
        .filter(|user| user.is_active)
        .map(|user| ProcessedUser::from(user))
        .collect()
}
```

## Trait Implementation Style

- Implement common traits (`Debug`, `Clone`, `PartialEq`) when appropriate
- Use `#[derive]` for automatic implementations when possible
- Implement traits manually only when custom behavior is needed

```rust
// Good: Appropriate trait derivations
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct UserId(u64);

#[derive(Debug, Clone)]
pub struct User {
    id: UserId,
    email: String,
    created_at: DateTime<Utc>,
}

// Good: Custom trait implementation when needed
impl Display for User {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "User({}): {}", self.id.0, self.email)
    }
}
```

## Code Quality Rules

### Implementation Scope

- Implement only what is explicitly requested
- Avoid adding features beyond requirements
- Ask before implementing related improvements
- Focus on minimal viable implementation

### Default Exclusions

Unless explicitly requested, do not add:

- Extensive input validation beyond basic needs
- Comprehensive error handling for edge cases
- Example implementations or demo code
- Complex optimization without profiling data

### Code Cleanliness

- Never generate comments in source code by default
- Use self-documenting code through clear naming
- Keep functions focused on single responsibility
- Maintain consistent code organization across modules
- Eliminate all unused code: imports, variables, functions

This document ensures consistent, high-quality Rust code style that follows established patterns from TypeScript/JavaScript projects while embracing Rust's unique features and idioms.